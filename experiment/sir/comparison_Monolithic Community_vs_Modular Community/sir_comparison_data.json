{"metadata": {"generated_at": "2025-08-24T13:06:33.520646", "comparison_type": "SIR curves and exponential model comparison", "dataset1": "Monolithic Community", "dataset2": "Modular Community", "total_rounds": 40, "rounds_range": "1-40", "description": "SIR model comparison data for virtual community experiment (total population only)"}, "rounds": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40], "dataset1": {"label": "Monolithic Community", "total_population": {"susceptible": [0.94, 0.92, 0.88, 0.85, 0.83, 0.8, 0.8, 0.78, 0.75, 0.73, 0.72, 0.7, 0.7, 0.67, 0.65, 0.65, 0.63, 0.61, 0.61, 0.61, 0.61, 0.61, 0.61, 0.61, 0.58, 0.55, 0.54, 0.54, 0.54, 0.53, 0.53, 0.53, 0.53, 0.53, 0.52, 0.52, 0.52, 0.51, 0.51, 0.51], "infected": [0.06, 0.07, 0.11, 0.14, 0.16, 0.19, 0.19, 0.21, 0.24, 0.25, 0.26, 0.28, 0.28, 0.3, 0.31, 0.3, 0.3, 0.32, 0.32, 0.3, 0.3, 0.29, 0.29, 0.29, 0.31, 0.33, 0.33, 0.33, 0.32, 0.33, 0.33, 0.33, 0.32, 0.32, 0.32, 0.32, 0.31, 0.3, 0.29, 0.28], "recovered": [0.0, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.03, 0.04, 0.05, 0.07, 0.07, 0.07, 0.09, 0.09, 0.1, 0.1, 0.1, 0.11, 0.12, 0.13, 0.13, 0.14, 0.14, 0.14, 0.14, 0.15, 0.15, 0.16, 0.16, 0.17, 0.19, 0.2, 0.21]}, "exponential_model": {"model_type": "exponential", "lambda_0": 0.33427375955167665, "lambda_1": -0.1, "mu_0": 0.021563433545154477, "mu_1": -0.008605080876351595}}, "dataset2": {"label": "Modular Community", "total_population": {"susceptible": [0.94, 0.91, 0.9, 0.9, 0.88, 0.85, 0.83, 0.8, 0.77, 0.73, 0.7, 0.68, 0.66, 0.64, 0.64, 0.63, 0.61, 0.58, 0.56, 0.55, 0.55, 0.55, 0.54, 0.54, 0.53, 0.53, 0.53, 0.53, 0.53, 0.52, 0.51, 0.51, 0.51, 0.5, 0.49, 0.49, 0.49, 0.49, 0.49, 0.49], "infected": [0.06, 0.09, 0.1, 0.1, 0.11, 0.14, 0.16, 0.18, 0.19, 0.23, 0.25, 0.27, 0.27, 0.28, 0.27, 0.27, 0.29, 0.31, 0.33, 0.34, 0.33, 0.32, 0.33, 0.32, 0.33, 0.32, 0.31, 0.29, 0.28, 0.28, 0.28, 0.27, 0.27, 0.27, 0.28, 0.28, 0.28, 0.27, 0.26, 0.25], "recovered": [0.0, 0.0, 0.0, 0.0, 0.01, 0.01, 0.01, 0.02, 0.04, 0.04, 0.05, 0.05, 0.07, 0.08, 0.09, 0.1, 0.1, 0.11, 0.11, 0.11, 0.12, 0.13, 0.13, 0.14, 0.14, 0.15, 0.16, 0.18, 0.19, 0.2, 0.21, 0.22, 0.22, 0.23, 0.23, 0.23, 0.23, 0.24, 0.25, 0.26]}, "exponential_model": {"model_type": "exponential", "lambda_0": 0.32857103207315547, "lambda_1": -0.08800859601286361, "mu_0": 0.031248402362066066, "mu_1": -0.008317309231221821}}}